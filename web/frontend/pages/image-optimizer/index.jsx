import { IndexTable, useBreakpoints } from "@shopify/polaris";
import { useTranslation } from "react-i18next";

import { isEmpty } from "lodash";
import { useSelector } from "react-redux";
import imageOptimization from "storeseo-enums/imageOptimization";
import TableEmptyState from "../../components/common/TableEmptyState.jsx";
import BulkImageOptimizationModal from "../../components/image-optimizer/BulkImageOptimizationModal.jsx";
import ImageRows from "../../components/images/ImageRows.jsx";
import { useStoreSeo } from "../../providers/StoreSeoProvider.jsx";

import useUserAddon from "@/hooks/useUserAddon.js";
import { useImageOptimizeActions, useImagesList } from "@/lib/hooks/image-optimizer";
import useIndexTablePagination from "@/lib/hooks/useIndexTablePagination.jsx";
import useIndexTableSort from "@/lib/hooks/useIndexTableSort.jsx";
import useIsRunningBackupRestore from "@/lib/hooks/useIsRunningBackupRestore.jsx";
import {
  generateBulkOptimizePayload,
  generateBulkRestorePayload,
} from "@/lib/utils/image-optimizer/imageOptimizerUtils.js";
import { useImageOptimizerLayoutContext } from "@/providers/ImageOptimizerLayoutProvider.jsx";
import { isSvgImage } from "@/utility/helpers.jsx";
import { useEffect, useState } from "react";
import ResourceType from "storeseo-enums/resourceType";

/**
 * @type {import("@shopify/polaris/build/ts/src/types.js").NonEmptyArray<import("@shopify/polaris/build/ts/src/components/IndexTable/IndexTable.js").IndexTableHeading>}
 */
const tableHeadings = [
  { title: "", key: "sl" },
  { title: "File name", key: "file_name" },
  { title: "Status", key: "optimization_status" },
  { title: "File size", key: "file_size" },
  { title: "Product", key: "product.title" },
  { title: "Action", key: "action", alignment: "center" },
];

function Images() {
  const { smDown: isSmallDevice } = useBreakpoints();
  const { t } = useTranslation();
  const user = useSelector((s) => s.user);
  const { doManualRefresh } = useStoreSeo();
  const { isRunning: isRunningBackupOrRestore } = useIsRunningBackupRestore();
  const { handleImagesUpdate, setImageToCompare } = useImageOptimizerLayoutContext();

  const { imageOptimizerUsageCount, imageOptimizerUsageLimit } = useUserAddon();

  // Use the reusable hook for images list
  const {
    images,
    formattedImages,
    pagination,
    hasEmptyContent,
    isLoading,
    refetchImages,
    queryKey,
    selectedResources,
    allResourcesSelected,
    handleSelectionChange,
    clearSelection,
    removeSelectedResources,
    selectableImagesCount,
  } = useImagesList(ResourceType.PRODUCT);

  // Use reusable image optimize actions hooks
  const { bulkOptimize, bulkRestore } = useImageOptimizeActions({
    queryKey,
    resourceType: ResourceType.PRODUCT,
    onOptimizeSuccess: () => {
      clearSelection();
      setShowBulkImageOptimizeModal(false);
    },
    onRestoreSuccess: () => {
      clearSelection();
    },
  });

  useEffect(() => {
    if (doManualRefresh) {
      refetchImages();
    }
  }, [doManualRefresh, refetchImages]);

  const resourceName = {
    singular: "image",
    plural: "images",
  };

  // Modal state
  const [showBulkImageOptimizeModal, setShowBulkImageOptimizeModal] = useState(false);

  useEffect(
    function removeUnoptimizableImagesFromSelection() {
      const imagesHash = images?.reduce((hash, img) => ({ ...hash, [img.id]: img }), {});

      const filteredResources = isRunningBackupOrRestore
        ? selectedResources.map((id) => id)
        : selectedResources.filter(
            (id) => imagesHash[id].optimization_status === imageOptimization.PENDING || isSvgImage(imagesHash[id].src)
          );

      if (filteredResources.length) removeSelectedResources(filteredResources);
    },
    [selectedResources, images, isRunningBackupOrRestore, removeSelectedResources]
  );

  const handleBulkOptimize = ({ compressionType, compressionFormat, resizeType }) => {
    const payload = generateBulkOptimizePayload({
      selectedResources,
      images,
      compressionType,
      compressionFormat,
      resizeType,
    });

    if (payload) {
      bulkOptimize.mutate(payload);
    }
  };

  const handleListRestore = () => {
    const payload = generateBulkRestorePayload({
      selectedResources,
      formattedImages,
    });

    if (payload) {
      bulkRestore.mutate(payload);
    }
  };

  const { handleSort, sortDir, sortIndex, defaultSortDir } = useIndexTableSort({ tableHeadings });
  const { paginationConfigs } = useIndexTablePagination(pagination);

  const emptyStateMarkup = (
    <TableEmptyState
      title="No images found"
      description="Try changing the filters or search term"
      withIllustration
    />
  );

  return !hasEmptyContent ? (
    <>
      <IndexTable
        condensed={isSmallDevice}
        resourceName={resourceName}
        itemCount={selectableImagesCount || images?.length || 0}
        selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
        onSelectionChange={(e, f, g) => {
          handleSelectionChange(e, f, g);
        }}
        headings={tableHeadings.map((heading) => ({
          ...heading,
          title: t(heading.title),
          alignment: heading.alignment || "start",
        }))}
        selectable={true}
        onSort={handleSort}
        sortable={[false, false, true, true, false, false]}
        sortColumnIndex={sortIndex}
        sortDirection={sortDir}
        defaultSortDirection={defaultSortDir}
        emptyState={emptyStateMarkup}
        promotedBulkActions={[
          {
            content: `${t("Restore")} (${selectedResources.length})`,
            disabled: bulkRestore.isLoading,
            onAction: handleListRestore,
          },
          {
            content: `${t("Optimize")} (${selectedResources.length})`,
            disabled:
              bulkRestore.isLoading || imageOptimizerUsageCount + selectedResources.length > imageOptimizerUsageLimit,
            onAction: () => setShowBulkImageOptimizeModal(true),
          },
        ]}
        pagination={paginationConfigs}
      >
        {formattedImages.map((image, index) => (
          <ImageRows
            image={image}
            key={index}
            rowPosition={index}
            isFetching={isLoading}
            selectedResources={selectedResources}
            onOptimize={(images) => handleImagesUpdate(images, queryKey)}
            onRestore={(images) => handleImagesUpdate(images, queryKey)}
            onCompare={setImageToCompare}
          />
        ))}
      </IndexTable>

      <BulkImageOptimizationModal
        isOpen={showBulkImageOptimizeModal}
        onClose={() => setShowBulkImageOptimizeModal(false)}
        onOptimize={handleBulkOptimize}
        selectedCount={selectedResources.length}
        isLoading={bulkOptimize.isLoading}
      />
    </>
  ) : null;
}

export default Images;
