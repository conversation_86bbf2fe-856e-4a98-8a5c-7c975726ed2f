# Image Optimizer Utilities

Reusable utilities for bulk image optimization and restoration across different image optimizer pages.

## Overview

This package provides:
1. **Payload Generators**: Functions to generate API payloads for bulk operations
2. **React Query Hooks**: Reusable hooks for bulk optimize and restore mutations
3. **Cache Management**: Utilities for updating React Query cache after operations

## Payload Generators (`imageOptimizerUtils.js`)

### `generateBulkOptimizePayload`

Generates payload for bulk image optimization.

```javascript
import { generateBulkOptimizePayload } from "@/lib/utils/image-optimizer/imageOptimizerUtils.js";

const payload = generateBulkOptimizePayload({
  selectedResources: ["1", "2", "3"], // Array of selected image IDs
  images: imagesArray,                // Array of image objects
  compressionType: "balanced",        // "none" | "lossless" | "balanced" | "lossy"
  compressionFormat: "jpeg",          // "none" | "jpeg"
  resizeType: "2048",                // "none" | "4000" | "2048" | "1600"
});

// Returns: { images: [{id, media_id}], setting: {compression_type, target_format, target_width} }
// Returns: null if no selected resources
```

### `generateBulkRestorePayload`

Generates payload for bulk image restoration.

```javascript
import { generateBulkRestorePayload } from "@/lib/utils/image-optimizer/imageOptimizerUtils.js";

const payload = generateBulkRestorePayload({
  selectedResources: ["1", "2", "3"], // Array of selected image IDs
  formattedImages: formattedImagesArray, // Array of formatted image objects
});

// Returns: [{id, media_id, resource_id?, originalSource, file_size}]
// Returns: null if no selected resources or no restorable images
```

### `updateImagesInCache`

Updates React Query cache with optimized/restored images.

```javascript
import { updateImagesInCache } from "@/lib/utils/image-optimizer/imageOptimizerUtils.js";

updateImagesInCache({
  queryClient,           // React Query client instance
  queryKey,             // Query key for the images list
  updatedImages,        // Array of updated image objects from API response
});
```

## React Query Hooks (`useBulkImageActions.jsx`)

### `useImageOptimizeActions` (Recommended)

Combined hook that provides both bulk optimize and restore functionality.

```javascript
import { useImageOptimizeActions } from "@/lib/hooks/image-optimizer";

function MyImageOptimizerPage() {
  const { bulkOptimize, bulkRestore, singleOptimize } = useImageOptimizeActions({
    queryKey,
    resourceType: ResourceType.PRODUCT, // or COLLECTION, ARTICLE
    onOptimizeSuccess: (data) => {
      // Custom success handler for optimization
      clearSelection();
      setShowModal(false);
    },
    onRestoreSuccess: (data) => {
      // Custom success handler for restoration
      clearSelection();
      console.log('Restored images:', data.images);
    },
    onSingleOptimizeSuccess: (data) => {
      // Custom success handler for single image optimization
      setSelectedImage(data.images[0]);
    },
  });

  // Use the mutations
  const { isLoading: isOptimizing, mutate: optimizeImages } = bulkOptimize;
  const { isLoading: isRestoring, mutate: restoreImages } = bulkRestore;

  const handleOptimize = ({ compressionType, compressionFormat, resizeType }) => {
    const payload = generateBulkOptimizePayload({
      selectedResources,
      images,
      compressionType,
      compressionFormat,
      resizeType,
    });

    if (payload) {
      optimizeImages(payload);
    }
  };

  const handleRestore = () => {
    const payload = generateBulkRestorePayload({
      selectedResources,
      formattedImages,
    });

    if (payload) {
      restoreImages(payload);
    }
  };
}
```

### Individual Hooks

You can also use the individual hooks if you only need one operation:

```javascript
import { useBulkImageOptimize, useBulkImageRestore } from "@/lib/hooks/image-optimizer";

// For optimization only
const { isLoading, mutate } = useBulkImageOptimize({
  queryClient,
  queryKey,
  clearSelection,
  onSuccess: (data) => console.log('Optimized!'),
});

// For restoration only
const { isLoading, mutate } = useBulkImageRestore({
  queryClient,
  queryKey,
  clearSelection,
  resourceType: ResourceType.PRODUCT,
  onSuccess: (data) => console.log('Restored!'),
});
```

## Complete Example

Here's how to use these utilities in a new image optimizer page:

```javascript
import { useBulkImageActions } from "@/lib/hooks/image-optimizer";
import { generateBulkOptimizePayload, generateBulkRestorePayload } from "@/lib/utils/image-optimizer/payloadGenerators.js";

function CollectionsImageOptimizer() {
  const {
    selectedResources,
    images,
    formattedImages,
    queryKey,
    clearSelection
  } = useImagesList(ResourceType.COLLECTION);

  const { bulkOptimize, bulkRestore } = useBulkImageActions({
    queryClient,
    queryKey,
    clearSelection,
    resourceType: ResourceType.COLLECTION,
    onOptimizeSuccess: () => setShowModal(false),
  });

  const handleBulkOptimize = ({ compressionType, compressionFormat, resizeType }) => {
    const payload = generateBulkOptimizePayload({
      selectedResources,
      images,
      compressionType,
      compressionFormat,
      resizeType,
    });

    if (payload) {
      bulkOptimize.mutate(payload);
    }
  };

  const handleBulkRestore = () => {
    const payload = generateBulkRestorePayload({
      selectedResources,
      formattedImages,
    });

    if (payload) {
      bulkRestore.mutate(payload);
    }
  };

  return (
    <IndexTable
      promotedBulkActions={[
        {
          content: `Restore (${selectedResources.length})`,
          disabled: bulkRestore.isLoading,
          onAction: handleBulkRestore,
        },
        {
          content: `Optimize (${selectedResources.length})`,
          disabled: bulkOptimize.isLoading,
          onAction: () => setShowOptimizeModal(true),
        },
      ]}
    >
      {/* Table content */}
    </IndexTable>
  );
}
```

## Benefits

1. **Code Reusability**: Same logic can be used across products, collections, and articles pages
2. **Consistency**: All pages use identical optimization and restoration logic
3. **Maintainability**: Changes to bulk operations only need to be made in one place
4. **Type Safety**: Proper TypeScript support with clear parameter types
5. **Error Handling**: Built-in error handling and loading states
6. **Cache Management**: Automatic React Query cache updates after operations
