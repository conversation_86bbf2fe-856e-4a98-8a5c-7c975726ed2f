//@ts-check
import { useMutation } from "react-query";
import { useImageApi } from "@/hooks";
import { updateImagesInCache } from "@/lib/utils/image-optimizer/payloadGenerators.js";
import useUserAddon from "@/hooks/useUserAddon.js";

/**
 * @typedef {keyof Pick<typeof import("storeseo-enums/resourceType"), "PRODUCT" | "COLLECTION" | "PAGE" | "ARTICLE">} ImageResourceType
 */

/**
 * Hook for bulk image optimization
 * @param {{
 *   queryClient: any,
 *   queryKey: any[],
 *   clearSelection: () => void,
 *   onSuccess?: (data: any) => void
 * }} params
 * @returns {{
 *   isLoading: boolean,
 *   mutate: (data: any) => void
 * }}
 */
export const useBulkImageOptimize = ({ queryClient, queryKey, clearSelection, onSuccess }) => {
  const imageApi = useImageApi();
  const { updateImageOptimizerUsage } = useUserAddon();

  const { isLoading, mutate } = useMutation({
    mutationFn: (data) => imageApi.optimizeImagesViaQueue(data),
    onSuccess: (data) => {
      // Update cache with optimized images
      updateImagesInCache({
        queryClient,
        queryKey,
        updatedImages: data.images,
      });

      // Clear selection and update usage
      clearSelection();
      updateImageOptimizerUsage(data.images.length);

      // Call custom onSuccess if provided
      if (onSuccess) {
        onSuccess(data);
      }
    },
  });

  return { isLoading, mutate };
};

/**
 * Hook for bulk image restoration
 * @param {{
 *   queryClient: any,
 *   queryKey: any[],
 *   clearSelection: () => void,
 *   resourceType: ImageResourceType,
 *   onSuccess?: (data: any) => void
 * }} params
 * @returns {{
 *   isLoading: boolean,
 *   mutate: (data: any) => void
 * }}
 */
export const useBulkImageRestore = ({ queryClient, queryKey, clearSelection, resourceType, onSuccess }) => {
  const imageApi = useImageApi();

  const { isLoading, mutate } = useMutation({
    mutationFn: (data) => imageApi.restoreImage(data, resourceType),
    onSuccess: (data) => {
      // Update cache with restored images
      updateImagesInCache({
        queryClient,
        queryKey,
        updatedImages: data.images,
      });

      // Clear selection
      clearSelection();

      // Call custom onSuccess if provided
      if (onSuccess) {
        onSuccess(data);
      }
    },
  });

  return { isLoading, mutate };
};

/**
 * Combined hook that provides both bulk optimize and restore functionality
 * @param {{
 *   queryClient: any,
 *   queryKey: any[],
 *   clearSelection: () => void,
 *   resourceType: ImageResourceType,
 *   onOptimizeSuccess?: (data: any) => void,
 *   onRestoreSuccess?: (data: any) => void
 * }} params
 * @returns {{
 *   bulkOptimize: {
 *     isLoading: boolean,
 *     mutate: (data: any) => void
 *   },
 *   bulkRestore: {
 *     isLoading: boolean,
 *     mutate: (data: any) => void
 *   }
 * }}
 */
export const useBulkImageActions = ({
  queryClient,
  queryKey,
  clearSelection,
  resourceType,
  onOptimizeSuccess,
  onRestoreSuccess,
}) => {
  const bulkOptimize = useBulkImageOptimize({
    queryClient,
    queryKey,
    clearSelection,
    onSuccess: onOptimizeSuccess,
  });

  const bulkRestore = useBulkImageRestore({
    queryClient,
    queryKey,
    clearSelection,
    resourceType,
    onSuccess: onRestoreSuccess,
  });

  return {
    bulkOptimize,
    bulkRestore,
  };
};
